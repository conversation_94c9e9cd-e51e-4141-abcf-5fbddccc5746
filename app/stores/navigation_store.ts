import { create } from 'zustand'

// Navigation state machine types
type NavigationState =
  | 'idle'
  | 'navigating_to_flow'
  | 'navigating_to_tree'
  | 'highlighting_flow'
  | 'highlighting_tree'

type NavigationEvent =
  | { type: 'NAVIGATE_TO_FLOW'; nodeId: string }
  | { type: 'NAVIGATE_TO_TREE'; nodeId: string }
  | { type: 'NAVIGATE_FROM_FLOW'; nodeId: string }
  | { type: 'HIGHLIGHT_COMPLETE' }
  | { type: 'RESET' }

// Global timeout controllers for cleanup
const timeoutControllers = new Map<string, AbortController>()

// Debounce controllers for preventing rapid navigation
const debounceControllers = new Map<string, AbortController>()

/**
 * Utility function to create a timeout with AbortController cleanup
 */
const createAbortableTimeout = (
  key: string,
  callback: () => void,
  delay: number
): void => {
  // Cancel any existing timeout for this key
  const existingController = timeoutControllers.get(key)
  if (existingController) {
    existingController.abort()
  }

  // Create new controller
  const controller = new AbortController()
  timeoutControllers.set(key, controller)

  const timeoutId = setTimeout(() => {
    if (!controller.signal.aborted) {
      callback()
      timeoutControllers.delete(key)
    }
  }, delay)

  // Listen for abort signal to clear timeout
  controller.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId)
    timeoutControllers.delete(key)
  })
}

/**
 * Utility function to create a debounced action with AbortController cleanup
 */
const createDebouncedAction = (
  key: string,
  action: () => void,
  delay: number = 100
): void => {
  // Cancel any existing debounce for this key
  const existingController = debounceControllers.get(key)
  if (existingController) {
    existingController.abort()
  }

  // Create new controller
  const controller = new AbortController()
  debounceControllers.set(key, controller)

  const timeoutId = setTimeout(() => {
    if (!controller.signal.aborted) {
      action()
      debounceControllers.delete(key)
    }
  }, delay)

  // Listen for abort signal to clear timeout
  controller.signal.addEventListener('abort', () => {
    clearTimeout(timeoutId)
    debounceControllers.delete(key)
  })
}

/**
 * Cleanup function to abort all pending timeouts and debounces
 */
export const cleanupNavigationTimeouts = (): void => {
  timeoutControllers.forEach(controller => controller.abort())
  timeoutControllers.clear()
  debounceControllers.forEach(controller => controller.abort())
  debounceControllers.clear()
}

/**
 * State machine transition logic
 */
const getNextState = (
  currentState: NavigationState,
  event: NavigationEvent
): NavigationState => {
  switch (currentState) {
    case 'idle':
      switch (event.type) {
        case 'NAVIGATE_TO_FLOW':
          return 'navigating_to_flow'
        case 'NAVIGATE_TO_TREE':
        case 'NAVIGATE_FROM_FLOW':
          return 'navigating_to_tree'
        default:
          return currentState
      }
    case 'navigating_to_flow':
      switch (event.type) {
        case 'HIGHLIGHT_COMPLETE':
          return 'highlighting_flow'
        case 'RESET':
          return 'idle'
        default:
          return currentState
      }
    case 'navigating_to_tree':
      switch (event.type) {
        case 'HIGHLIGHT_COMPLETE':
          return 'highlighting_tree'
        case 'RESET':
          return 'idle'
        default:
          return currentState
      }
    case 'highlighting_flow':
    case 'highlighting_tree':
      switch (event.type) {
        case 'RESET':
          return 'idle'
        case 'NAVIGATE_TO_FLOW':
          return 'navigating_to_flow'
        case 'NAVIGATE_TO_TREE':
        case 'NAVIGATE_FROM_FLOW':
          return 'navigating_to_tree'
        default:
          return currentState
      }
    default:
      return currentState
  }
}

type NavigationStore = {
  // State machine state
  navigationState: NavigationState

  // React Flow navigation
  targetNodeId: string | null
  setTargetNodeId: (nodeId: string | null) => void
  navigateToNode: (nodeId: string) => void
  // URL-specific navigation with longer TTL so canvases can initialize
  navigateToNodeFromUrl: (nodeId: string) => void

  // Tree View navigation (reverse direction)
  treeTargetNodeId: string | null
  setTreeTargetNodeId: (nodeId: string | null) => void
  navigateToTreeNode: (nodeId: string) => void

  // Flag to prevent React Flow auto-focus when navigation comes from React Flow
  preventReactFlowFocus: boolean
  navigateToTreeNodeFromReactFlow: (nodeId: string) => void

  // Navigation state tracking to prevent race conditions
  isNavigating: boolean
  lastNavigationTime: number
  lastNavigationSource: 'outline' | 'visualization' | null

  // URL focus parameter callback
  onNodeFocusChange: ((nodeId: string | null) => void) | null
  setOnNodeFocusChange: (
    callback: ((nodeId: string | null) => void) | null
  ) => void

  // State machine dispatch
  dispatch: (event: NavigationEvent) => void

  // Cleanup function
  cleanup: () => void
}

export const useNavigationStore = create<NavigationStore>((set, get) => ({
  navigationState: 'idle',
  targetNodeId: null,
  treeTargetNodeId: null,
  preventReactFlowFocus: false,
  isNavigating: false,
  lastNavigationTime: 0,
  lastNavigationSource: null,
  onNodeFocusChange: null,

  setTargetNodeId: nodeId => set({ targetNodeId: nodeId }),
  setTreeTargetNodeId: nodeId => set({ treeTargetNodeId: nodeId }),
  setOnNodeFocusChange: callback => set({ onNodeFocusChange: callback }),

  dispatch: (event: NavigationEvent) => {
    const state = get()
    const nextState = getNextState(state.navigationState, event)

    if (nextState !== state.navigationState) {
      console.log(
        `🔄 Navigation state: ${state.navigationState} -> ${nextState}`
      )
      set({ navigationState: nextState })
    }
  },

  navigateToNode: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'outline' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from outline')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_TO_FLOW', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-to-node-${nodeId}`,
      () => {
        set({
          targetNodeId: nodeId,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'outline',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete after setting target
        setTimeout(() => {
          const currentState = get()
          currentState.dispatch({ type: 'HIGHLIGHT_COMPLETE' })
        }, 100)

        // Clear the target after a delay with proper cleanup
        createAbortableTimeout(
          `clear-navigate-to-node-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              targetNodeId: null,
              isNavigating: false,
            })
          },
          2000
        )
      },
      100
    )
  },

  // Longer-lived focus signal for deep-link/URL navigation to avoid race with canvas readiness
  navigateToNodeFromUrl: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'outline' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from outline')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_TO_FLOW', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-to-node-from-url-${nodeId}`,
      () => {
        set({
          targetNodeId: nodeId,
          treeTargetNodeId: nodeId,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'outline',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete after setting target
        setTimeout(() => {
          const currentState = get()
          currentState.dispatch({ type: 'HIGHLIGHT_COMPLETE' })
        }, 100)

        // Keep target longer (8s) to allow diagrams to finish initializing
        createAbortableTimeout(
          `clear-navigate-to-node-from-url-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              targetNodeId: null,
              treeTargetNodeId: null,
              isNavigating: false,
            })
          },
          8000
        )
      },
      100
    )
  },

  navigateToTreeNode: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'visualization' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from visualization')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_TO_TREE', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-to-tree-node-${nodeId}`,
      () => {
        set({
          treeTargetNodeId: nodeId,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'visualization',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete after setting target
        setTimeout(() => {
          const currentState = get()
          currentState.dispatch({ type: 'HIGHLIGHT_COMPLETE' })
        }, 100)

        // Clear the target after a delay with proper cleanup
        createAbortableTimeout(
          `clear-navigate-to-tree-node-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              treeTargetNodeId: null,
              isNavigating: false,
            })
          },
          3000
        )
      },
      100
    )
  },

  navigateToTreeNodeFromReactFlow: nodeId => {
    const currentTime = Date.now()
    const state = get()

    // Prevent rapid navigation from same source
    if (
      state.isNavigating &&
      state.lastNavigationSource === 'visualization' &&
      currentTime - state.lastNavigationTime < 300
    ) {
      console.log('🚫 Navigation debounced: too rapid from React Flow')
      return
    }

    // Dispatch state machine event
    state.dispatch({ type: 'NAVIGATE_FROM_FLOW', nodeId })

    // Use debounced action to prevent race conditions
    createDebouncedAction(
      `navigate-from-flow-${nodeId}`,
      () => {
        set({
          treeTargetNodeId: nodeId,
          preventReactFlowFocus: true,
          isNavigating: true,
          lastNavigationTime: currentTime,
          lastNavigationSource: 'visualization',
        })

        // Notify focus parameter callback with error handling
        const currentState = get()
        if (currentState.onNodeFocusChange) {
          try {
            currentState.onNodeFocusChange(nodeId)
          } catch (error) {
            console.warn('Focus parameter callback error:', error)
          }
        }

        // Dispatch highlight complete after setting target
        setTimeout(() => {
          const currentState = get()
          currentState.dispatch({ type: 'HIGHLIGHT_COMPLETE' })
        }, 100)

        // Clear the target and reset the flag after highlighting with proper cleanup
        createAbortableTimeout(
          `clear-navigate-from-flow-${nodeId}`,
          () => {
            const currentState = get()
            currentState.dispatch({ type: 'RESET' })
            set({
              treeTargetNodeId: null,
              preventReactFlowFocus: false,
              isNavigating: false,
            })
          },
          3000
        )
      },
      100
    )
  },

  cleanup: () => {
    cleanupNavigationTimeouts()
  },
}))
