'use client'

import { useEffect, useCallback, useRef } from 'react'
import { useSearchPara<PERSON>, useRouter, usePathname } from 'next/navigation'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { useTabStore } from '@/app/(conv)/dragTree/[dragTreeId]/stores/useTabStore'

/**
 * Hook for managing URL focus parameter functionality
 *
 * Features:
 * - Parses focus parameter from URL on page load
 * - Automatically navigates to focused node when tree is loaded
 * - Updates URL with focus parameter when nodes are clicked
 * - Handles fallback behavior for invalid node IDs
 * - Integrates with existing navigation system
 */
export const useFocusParameter = () => {
  const searchParams = useSearchParams()
  const router = useRouter()
  const pathname = usePathname()
  const { navigateToNodeFromUrl, setOnNodeFocusChange } = useNavigationStore()
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const setActiveTab = useTabStore(state => state.setActiveTab)

  // Track if we've already processed the initial focus parameter
  const hasProcessedInitialFocus = useRef<boolean>(false)
  const currentFocusNodeId = useRef<string | null>(null)
  const reactFlowReadyRef = useRef<boolean>(false)
  const hasAttemptedInitialFocus = useRef<boolean>(false)

  useEffect(() => {
    const markReady = () => {
      reactFlowReadyRef.current = true
    }

    window.addEventListener('reactflow-ready', markReady)
    window.addEventListener('reactflow-diagram-ready', markReady)

    return () => {
      window.removeEventListener('reactflow-ready', markReady)
      window.removeEventListener('reactflow-diagram-ready', markReady)
    }
  }, [])

  /**
   * Check if a node exists in the current tree structure
   */
  const nodeExists = useCallback(
    (nodeId: string): boolean => {
      if (!frontendTreeStructure) return false

      const checkNodeRecursively = (node: any): boolean => {
        if (node.id === nodeId) return true
        if (node.children) {
          return node.children.some((child: any) => checkNodeRecursively(child))
        }
        return false
      }

      return checkNodeRecursively(frontendTreeStructure)
    },
    [frontendTreeStructure]
  )

  /**
   * Update URL with focus parameter
   */
  const updateFocusParameter = useCallback(
    (nodeId: string | null) => {
      // Avoid updating if the focus hasn't actually changed
      if (currentFocusNodeId.current === nodeId) return

      currentFocusNodeId.current = nodeId

      const params = new URLSearchParams(searchParams.toString())

      if (nodeId) {
        params.set('focus', nodeId)
      } else {
        params.delete('focus')
      }

      // Use replace to avoid adding to browser history for every node click
      const newUrl = `${pathname}?${params.toString()}`
      router.replace(newUrl, { scroll: false })
    },
    [searchParams, pathname, router]
  )

  /**
   * Manually process initial focus from URL parameter.
   * Call this when the UI is ready to react to navigation.
   */
  const processInitialFocus = useCallback(() => {
    if (hasAttemptedInitialFocus.current) {
      return
    }

    const focusParam = searchParams.get('focus')

    if (focusParam) {
      try {
        console.log(
          '🎯 [useFocusParameter] Focus parameter detected, ensuring main tab is active.'
        )
        setActiveTab('main')
      } catch (error) {
        console.error(
          '[useFocusParameter] Failed to set active tab to main:',
          error
        )
      }
    }

    if (
      !hasProcessedInitialFocus.current &&
      focusParam &&
      frontendTreeStructure
    ) {
      hasAttemptedInitialFocus.current = true
      const tryFocus = () => {
        if (!nodeExists(focusParam)) {
          console.warn(
            `⚠️ [useFocusParameter] Node ${focusParam} not found in tree, ignoring focus parameter`
          )
          const params = new URLSearchParams(searchParams.toString())
          params.delete('focus')
          const newUrl = `${pathname}?${params.toString()}`
          router.replace(newUrl, { scroll: false })
          hasProcessedInitialFocus.current = true
          return
        }

        console.log(
          `🎯 [useFocusParameter] Focusing on node from URL: ${focusParam}`
        )
        // Use URL-specific navigation which holds target longer to let canvases initialize
        navigateToNodeFromUrl(focusParam)
        currentFocusNodeId.current = focusParam
        hasProcessedInitialFocus.current = true
      }

      const scheduleFocus = () => {
        setTimeout(tryFocus, 100)
      }

      if (reactFlowReadyRef.current) {
        scheduleFocus()
      } else {
        let fallbackTimer: ReturnType<typeof setTimeout> | null = null

        const cleanup = () => {
          window.removeEventListener('reactflow-ready', handleReady)
          window.removeEventListener('reactflow-diagram-ready', handleReady)
          if (fallbackTimer) {
            clearTimeout(fallbackTimer)
            fallbackTimer = null
          }
        }

        const handleReady = () => {
          reactFlowReadyRef.current = true
          cleanup()
          scheduleFocus()
        }

        window.addEventListener('reactflow-ready', handleReady)
        window.addEventListener('reactflow-diagram-ready', handleReady)

        fallbackTimer = setTimeout(() => {
          cleanup()
          scheduleFocus()
        }, 2000)
      }
    }
  }, [
    searchParams,
    frontendTreeStructure,
    nodeExists,
    navigateToNodeFromUrl,
    pathname,
    router,
    setActiveTab,
  ])

  /**
   * Register callback with navigation store to update URL when nodes are clicked
   */
  useEffect(() => {
    setOnNodeFocusChange(updateFocusParameter)

    // Cleanup callback on unmount
    return () => {
      setOnNodeFocusChange(null)
    }
  }, [setOnNodeFocusChange, updateFocusParameter])

  /**
   * Reset processing flag when tree changes (e.g., navigating to different drag tree)
   */
  useEffect(() => {
    hasProcessedInitialFocus.current = false
    currentFocusNodeId.current = null
    hasAttemptedInitialFocus.current = false
  }, [frontendTreeStructure?.id])

  return {
    updateFocusParameter,
    currentFocusNodeId: currentFocusNodeId.current,
    processInitialFocus,
    hasAttemptedInitialFocus: hasAttemptedInitialFocus.current,
  }
}
